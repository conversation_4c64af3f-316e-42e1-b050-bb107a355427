using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.Fonts;
using SixLabors.ImageSharp.Formats.Jpeg;
using System;
using System.IO;
using System.Threading.Tasks;

namespace RealEstate.Application.Interfaces
{
    public interface IImageProcessingService
    {
        /// <summary>
        /// Resizes an image to the specified dimensions
        /// </summary>
        /// <param name="inputPath">Path to the source image</param>
        /// <param name="outputPath">Path where the resized image will be saved</param>
        /// <param name="width">Desired width (null for auto-scaling based on height)</param>
        /// <param name="height">Desired height (null for auto-scaling based on width)</param>
        /// <param name="preserveAspectRatio">Whether to maintain the original aspect ratio</param>
        /// <returns>Path to the resized image</returns>
        Task<string> ResizeImageAsync(string inputPath, string outputPath, int? width, int? height, bool preserveAspectRatio = true);

        /// <summary>
        /// Adds a watermark to an image
        /// </summary>
        /// <param name="inputPath">Path to the source image</param>
        /// <param name="outputPath">Path where the watermarked image will be saved</param>
        /// <param name="watermarkText">Text to use as watermark</param>
        /// <param name="opacity">Opacity of the watermark (0.0 to 1.0)</param>
        /// <returns>Path to the watermarked image</returns>
        Task<string> AddWatermarkAsync(string inputPath, string outputPath, string watermarkText, float opacity = 0.5f);

        /// <summary>
        /// Process image by resizing and adding watermark in a single operation
        /// </summary>
        /// <param name="inputPath">Path to the source image</param>
        /// <param name="outputPath">Path where the processed image will be saved</param>
        /// <param name="width">Desired width (null for auto-scaling based on height)</param>
        /// <param name="height">Desired height (null for auto-scaling based on width)</param>
        /// <param name="preserveAspectRatio">Whether to maintain the original aspect ratio</param>
        /// <param name="watermarkText">Text to use as watermark</param>
        /// <param name="opacity">Opacity of the watermark (0.0 to 1.0)</param>
        /// <returns>Path to the processed image</returns>
        Task<string> ProcessImageAsync(string inputPath, string outputPath, int? width, int? height, bool preserveAspectRatio = true, string watermarkText = null, float opacity = 0.5f);

        /// <summary>
        /// Generate various sizes of an image (thumbnail, medium, large)
        /// </summary>
        /// <param name="inputPath">Path to the source image</param>
        /// <param name="outputDirectory">Directory where the sized images will be saved</param>
        /// <param name="baseFilename">Base filename to use (without extension)</param>
        /// <param name="addWatermark">Whether to add watermark to the images</param>
        /// <param name="watermarkText">Text to use as watermark</param>
        /// <returns>Dictionary of size name to file path</returns>
        Task<Dictionary<string, string>> GenerateImageSizesAsync(string inputPath, string outputDirectory, string baseFilename, string watermarkText = "YEZ HOME");
    }
}