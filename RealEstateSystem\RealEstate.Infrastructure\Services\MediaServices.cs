using AutoMapper;
using Microsoft.AspNetCore.Http;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using RealEstate.Infrastructure.Services.FileStorage;
using Shared.Enums;
using Shared.Results;
using System.IO;

namespace RealEstate.Infrastructure.Services
{
    public class MediaServices : IMediaServices
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IImageProcessingService _imageProcessingService;
        private readonly IPropertyService _propertyService;
        private readonly IFileStorageService _fileStorageService;

        public MediaServices(IUnitOfWork unitOfWork, IMapper mapper, IImageProcessingService imageProcessingService, IPropertyService propertyService, IFileStorageService fileStorageService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _imageProcessingService = imageProcessingService;
            _propertyService = propertyService;
            _fileStorageService = fileStorageService;
        }

        public async Task<Result<IEnumerable<PropertyMediaDto>>> GetAllMediaAsync(List<Guid> mediaIds)
        {
            var medias = await _unitOfWork.PropertyMedias.FindAsync(x => mediaIds.Contains(x.Id) && x.PropertyID == null);
            return Result<IEnumerable<PropertyMediaDto>>.Success(_mapper.Map<IEnumerable<PropertyMediaDto>>(medias));
        }

        public async Task<Result<PropertyMediaDto>> GetMediaByIdAsync(Guid id)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id, true, null);
            if (media == null)
            {
                return Result<PropertyMediaDto>.Failure("Media not found.", ErrorType.NotFound);
            }
            return Result<PropertyMediaDto>.Success(_mapper.Map<PropertyMediaDto>(media));
        }

        public async Task<Result<IEnumerable<PropertyMediaDto>>> GetAllMediaAsync()
        {
            var medias = await _unitOfWork.PropertyMedias.GetAllAsync();
            return Result<IEnumerable<PropertyMediaDto>>.Success(_mapper.Map<IEnumerable<PropertyMediaDto>>(medias));
        }

        public async Task<Result<IEnumerable<PropertyMediaDto>>> GetMediaByPropertyIdAsync(Guid propertyId)
        {
            var medias = await _unitOfWork.PropertyMedias.FindAsync(m => m.PropertyID == propertyId);
            return Result<IEnumerable<PropertyMediaDto>>.Success(_mapper.Map<IEnumerable<PropertyMediaDto>>(medias));
        }

        public async Task<Result<PropertyMediaDto>> CreateMediaAsync(CreateMediaDto mediaDto)
        {
            var media = _mapper.Map<PropertyMedia>(mediaDto);
            await _unitOfWork.PropertyMedias.AddAsync(media);
            await _unitOfWork.SaveChangesAsync();
            return Result<PropertyMediaDto>.Success(_mapper.Map<PropertyMediaDto>(media));
        }

        public async Task<Result<PropertyMediaDto>> UpdateMediaAsync(Guid id, CreateMediaDto mediaDto)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null)
            {
                return Result<PropertyMediaDto>.Failure("Media not found.", ErrorType.NotFound);
            }

            _mapper.Map(mediaDto, media);
            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return Result<PropertyMediaDto>.Success(_mapper.Map<PropertyMediaDto>(media));
        }

        public async Task<Result> DeleteMediaAsync(Guid id)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null)
            {
                return Result.Failure("Media not found.", ErrorType.NotFound);
            }

            _unitOfWork.PropertyMedias.Remove(media);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public Task<Result<List<Guid>>> UpdateMediaAsync(Guid propertyId, List<Guid> mediaIds)
        {
            throw new NotImplementedException();
        }

        public async Task<Result<Guid>> UpdateMediaAsync(Guid mediaId, Guid propertyId, string filePath, PropertyMediaDto propertyMedia)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(mediaId);
            if (media == null)
            {
                return Result<Guid>.Failure("Media not found.", ErrorType.NotFound);
            }

            media.FilePath = filePath;
            media.PropertyID = propertyId;
            media.Caption = propertyMedia.Caption;
            media.IsAvatar = propertyMedia.IsAvatar;

            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return Result<Guid>.Success(mediaId);
        }

        public async Task<Result<IEnumerable<PropertyMediaDto>>> UploadPropertyImagesAsync(Guid? propertyId, IFormFileCollection files, string scheme, string host)
        {
            if (files == null || files.Count == 0)
            {
                return Result<IEnumerable<PropertyMediaDto>>.Failure("No files uploaded.", ErrorType.Validation);
            }

            // Validate property exists if propertyId is provided
            if (propertyId != null && propertyId != Guid.Empty)
            {
                var propertyResult = await _propertyService.GetPropertyByIdAsync(propertyId.Value);
                if (!propertyResult.IsSuccess)
                {
                    return Result<IEnumerable<PropertyMediaDto>>.Failure("Property not found.", ErrorType.NotFound);
                }
            }

            // Get the appropriate upload folder
            string uploadFolder = _fileStorageService.GetPropertyImageFolder(propertyId);

            // Ensure folder exists
            var folderResult = await _fileStorageService.EnsureFolderExistsAsync(uploadFolder);
            if (!folderResult.IsSuccess)
            {
                return Result<IEnumerable<PropertyMediaDto>>.Failure(folderResult.ErrorMessage, folderResult.ErrorType ?? ErrorType.Validation);
            }

            var uploadedFiles = new List<PropertyMediaDto>();

            foreach (var file in files)
            {
                if (!file.ContentType.StartsWith("image/")) continue;

                var fileId = Guid.NewGuid();
                var extension = Path.GetExtension(file.FileName);
                var baseFilename = fileId.ToString();
                var watermarkText = "YEZ HOME";

                Dictionary<string, string> processedImages;
                string tempLocalPath = null;

                try
                {
                    // For S3 storage, we need to process images locally first, then upload
                    if (_fileStorageService is S3StorageService)
                    {
                        // Create a temporary local file for image processing
                        var tempLocalFolder = Path.Combine(Path.GetTempPath(), "ImageProcessing", DateTime.Now.ToString("yyyyMMdd"));
                        Directory.CreateDirectory(tempLocalFolder);
                        tempLocalPath = Path.Combine(tempLocalFolder, $"temp_{fileId}{extension}");

                        // Save file locally for processing
                        using (var stream = new FileStream(tempLocalPath, FileMode.Create))
                        {
                            await file.CopyToAsync(stream);
                        }

                        // Process images locally
                        processedImages = await _imageProcessingService.GenerateImageSizesAsync(tempLocalPath, tempLocalFolder, baseFilename, watermarkText);

                        // Upload all processed images to S3
                        var uploadedPaths = new Dictionary<string, string>();
                        foreach (var processedImage in processedImages)
                        {
                            var sizeName = processedImage.Key;
                            var localPath = processedImage.Value;
                            var s3FileName = $"{baseFilename}_{sizeName}{extension}";

                            // Read file bytes and upload directly
                            var fileBytes = await File.ReadAllBytesAsync(localPath);
                            var uploadResult = await _fileStorageService.UploadFileAsync(fileBytes, uploadFolder, s3FileName, file.ContentType);

                            if (uploadResult.IsSuccess)
                            {
                                uploadedPaths[sizeName] = uploadResult.Value;
                            }

                            // Clean up local processed file
                            if (File.Exists(localPath))
                            {
                                File.Delete(localPath);
                            }
                        }

                        processedImages = uploadedPaths;
                    }
                    else
                    {
                        // For local storage, upload first then process
                        var tempFileName = $"temp_{fileId}{extension}";
                        var uploadResult = await _fileStorageService.UploadFileAsync(file, uploadFolder, tempFileName);
                        if (!uploadResult.IsSuccess)
                        {
                            continue; // Skip this file and continue with others
                        }

                        tempLocalPath = uploadResult.Value;
                        processedImages = await _imageProcessingService.GenerateImageSizesAsync(tempLocalPath, uploadFolder, baseFilename, watermarkText);

                        // Clean up temporary file
                        await _fileStorageService.DeleteFileAsync(tempLocalPath);
                    }

                    if (processedImages.ContainsKey("original"))
                    {
                        string publicUrl = _fileStorageService.GeneratePublicUrl(processedImages["original"], scheme, host);
                        var media = new CreateMediaDto
                        {
                            Id = fileId,
                            PropertyID = propertyId,
                            MediaType = file.ContentType,
                            MediaURL = publicUrl,
                            FilePath = processedImages["original"],
                            ThumbnailPath = processedImages.GetValueOrDefault("thumbnail"),
                            SmallPath = processedImages.GetValueOrDefault("small"),
                            MediumPath = processedImages.GetValueOrDefault("medium"),
                            LargePath = processedImages.GetValueOrDefault("large"),
                            UploadedAt = DateTime.UtcNow
                        };

                        var createdMediaResult = await CreateMediaAsync(media);

                        if (createdMediaResult.Value != null)
                            uploadedFiles.Add(createdMediaResult.Value);
                    }
                }
                finally
                {
                    // Clean up temporary local file if it exists
                    if (!string.IsNullOrEmpty(tempLocalPath) && File.Exists(tempLocalPath))
                    {
                        File.Delete(tempLocalPath);
                    }

                    // Clean up temporary local folder if using S3
                    if (_fileStorageService is S3StorageService && !string.IsNullOrEmpty(tempLocalPath))
                    {
                        var tempFolder = Path.GetDirectoryName(tempLocalPath);
                        if (Directory.Exists(tempFolder) && Directory.GetFiles(tempFolder).Length == 0)
                        {
                            Directory.Delete(tempFolder);
                        }
                    }
                }
            }

            return Result<IEnumerable<PropertyMediaDto>>.Success(uploadedFiles);
        }

        public async Task<Result> UpdateMediaCaptionAsync(Guid id, string caption)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null)
            {
                return Result.Failure("Media not found.", ErrorType.NotFound);
            }

            media.Caption = caption;
            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> UpdateMediaIsAvatarAsync(Guid id, bool isAvatar)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null)
            {
                return Result.Failure("Media not found.", ErrorType.NotFound);
            }

            if (media.PropertyID == null)
            {
                return Result.Failure("This media is not associated with any property.", ErrorType.Validation);
            }

            if (isAvatar)
            {
                var otherMedia = await _unitOfWork.PropertyMedias.FindAsync(
                    m => m.PropertyID == media.PropertyID && m.Id != id && m.IsAvatar == true);
                foreach (var item in otherMedia)
                {
                    item.IsAvatar = false;
                    _unitOfWork.PropertyMedias.Update(item);
                }
            }

            media.IsAvatar = isAvatar;
            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public string GetMediaPath(PropertyMediaDto media, string size)
        {
            switch (size?.ToLowerInvariant())
            {
                case "thumbnail":
                    return !string.IsNullOrEmpty(media.ThumbnailURL) ? media.ThumbnailURL! : media.FilePath!;
                case "small":
                    return !string.IsNullOrEmpty(media.SmallURL) ? media.SmallURL! : media.FilePath!;
                case "medium":
                    return !string.IsNullOrEmpty(media.MediumURL) ? media.MediumURL! : media.FilePath!;
                case "large":
                    return !string.IsNullOrEmpty(media.LargeURL) ? media.LargeURL! : media.FilePath!;
                default:
                    return media.FilePath!;
            }
        }
    }
}